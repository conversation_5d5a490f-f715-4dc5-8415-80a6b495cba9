{"groupId": "cn.hexop.agnes", "options": {"deployOnRelease": true, "waitForSonarQualityGate": false, "nodejsVersion": "20", "pnpmAutoUpBeta": "@agnes"}, "attrs": {"artifactId": "agne<PERSON>-ui", "pkgRemotePath": "/@agnes/agnes-ui", "pkgDevRepoKey": "agnes-npm-dev", "pkgReleaseRepoKey": "agnes-npm-release", "deployUser": "hexapp", "deployToPath": "/app/agnes-ui", "installWorkDir": "/hexapp/install", "npmDevRepoKey": "agnes-npm-dev"}, "steps": {"scan": {"skip.dev": true, "skip.release": true}, "unitTest": {"skip.dev": true, "skip.release": true}, "uploadPackage": {"sh": " pnpm publish --no-git-checks"}, "compile": {"sh": " pnpm install --no-frozen-lockfile && pnpm build"}}, "deploy": {"deployType": "ssh", "artifact": {"artifactId": "${attrs.artifactId}"}, "scripts": [{"type": "scp", "from": "${project.devopsDir}/deploy/deploy-dev.sh", "to": "${deploy.deployToPath}"}, {"type": "ssh", "local": false, "sh": "bash ${deploy.deployToPath}/deploy-dev.sh"}], "envs": [{"name": "dev", "matchBranch": "dev", "deployUser": "root", "deployToServer": "************"}]}}
<html>
<head>
    <script type="text/javascript" src="../lib/jQuery/jquery.js"></script>
    <script type="text/javascript">

        $(function () {
            doSubmit(); 
        })

        function doSubmit() {
            const searUrlStr = window.location.search
            const paramsObj = searUrlStr.split(';');
            var surl = paramsObj[0];
            var url = paramsObj[1];
              // 使用 CORS 代理服务器
            const corsProxy = 'https://cors-anywhere.herokuapp.com/';
            const targetUrl = `${corsProxy}${surl}`;

            jQuery.ajax({
                url: targetUrl,
                async: true,
                success: function (res) {
                    if (res.errorCode) {
                        alert("用户名或密码错误"); //登录失败（用户名或密码错误）

                    } else {
                        //登录成功
                        window.location = url; //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                    }
                },
                error: function (res) {
                    if (res.errorCode) {
                        alert("登录失败服务器超时或其他错误"); //登录失败（用户名或密码错误）

                    } else {
                        //登录成功
                        window.location = url; //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                    }
                }
            });
        }
    </script>
</head>
<body>
<form id="login" name="login" method="POST" style="display:none">
    <p>
        <input id="username" type="text" autocomplete="off"/>
    </p>
    <p>
        <input id="password" type="password" autocomplete="off"/>
    </p>
    <input type="button" value="登录" onClick="doSubmit()"/>
</form>
<div style="height:100%;width:100%;text-align:center;display:block;line-height:100%;font-size:40px"> 登陆中,请稍候……</div>
</body>
</html>

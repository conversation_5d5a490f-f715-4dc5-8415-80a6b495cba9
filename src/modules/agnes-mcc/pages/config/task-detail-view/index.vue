<template>
  <div class="task-detail-view">
    <div class="return-button" v-if="isShow &&$app.dict.getDictName('AGNES_CJHX_BUTTON', 'cancelBtn') == 'true'">
        <gf-button class="add-btn" size="mini" @click="jumpPage">返回</gf-button>
    </div>
    <div class="task-query" :class="[collapsed ? 'task-query-fold' : 'task-query-unFold']">
      <el-form
        ref="form"
        label-position="right"
        :model="queryArgs"
        label-width="100px"
        size="mini">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="任务名称:">
              <el-input v-model="queryArgs.taskName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属岗位:">
              <select-picker
                placeholder="请选择负责人岗位"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.belongPost"
                @change="handlePostChange"
                :options="postList.map(_=>({value:_.id, label:_.postName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="任务状态:">
              <select-picker
                collapse-tags
                clearable
                filterable
                :isString="true"
                v-model="queryArgs.taskStatus"
                :options="taskStatusList.map(_=>({value:_.dictId, label:_.dictName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-show="!collapsed">
            <el-form-item label="责任人:">
              <select-picker
                placeholder="请选择责任人"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.attUser"
                :options="attUser.map(_=>({value:_.userId, label:_.userName}))"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-show="!collapsed">
          <el-col :span="6">
            <el-form-item label="任务日期:">
              <el-date-picker
                class="date-picker"
                v-model="taskTimeRange"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                @change="taskTimeChange" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品代码:">
              <select-picker
                placeholder="请选择产品代码"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.prdtCode"
                :options="prdtCode.map(_=>({value:_.productCode, label:_.productName+ '-' + _.productCode}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="剩余办理天数:">
              <gf-input v-model="queryArgs.residueTime"></gf-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="执行人:">
              <select-picker
                placeholder="请选择执行人"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.executeUser"
                :options="attUser.map(_=>({value:_.userId, label:_.userName}))"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="6" v-show="!collapsed">
            <el-form-item label="业务日期:">
              <el-date-picker
                class="date-picker"
                v-model="bizTimeRange"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                @change="bizTimeChange" />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-show="!collapsed">
            <el-form-item label="任务等级:">
              <select-picker
                placeholder="请选择任务等级"
                collapse-tags
                clearable
                filterable
                :isString="true"
                v-model="queryArgs.taskLevel"
                :options="taskLevelList.map(_=>({value:_.dictId, label:_.dictName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="collapsed ? 6 : 12">
            <div :class="['button-container', collapsed ? 'button-fold' : 'button-not-fold']">
              <gf-button
                @click="getQueryData()"
                class="option-btn"
                type="primary">查询</gf-button>
              <gf-button @click="reSetSearch()" class="option-btn">重置</gf-button>
              <gf-button @click="hideQueryParam()" class="option-btn">{{ collapsed ? '展开条件' : '收起条件' }} <i
                  :class="[
                    collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up',
                  ]"></i></gf-button>

            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <gf-grid
      :filterRemote="false"
      grid-no="agnes-task-detail"
      @row-double-click="showTaskDetail"
      @selected-changed="selectedChanged"
      ref="taskGrid"
      toolbar="find,more"
      style="height: calc(100% - 180px) !important;"
      :options="addLineBgGridOptions"
      :query-args="queryFormArgs">
      <template slot="left">
        <el-radio-group
          v-model="queryArgs.authQueryParam1"
          @input="getQueryData">
          <el-radio :label="1" v-if="$hasPermission('agnes.task.detail.view.myTask')">我的任务</el-radio>
          <el-radio :label="2" v-if="$hasPermission('agnes.task.detail.view.centerTask')">中心任务</el-radio>
          <el-radio :label="3" v-if="$hasPermission('agnes.task.detail.view.allTask')">全部任务</el-radio>
        </el-radio-group>
      </template>
    </gf-grid>
    <el-drawer :title="clickTaskInfo.taskName" :visible.sync="taskInfoDrawer" direction="rtl">
      <process-scene-task />
    </el-drawer>
  </div>
</template>
<script>
import processSceneTask from '../task-center/components/panel/process-scene-task'


export default {
  props:{
    isShow:{
      default:false,
      type:Boolean
    }
  },
  components: {
    processSceneTask,
  },
  data() {
    return {
      taskTimeRange: this.getDefaultTodayRange(),
      bizTimeRange: [],
      clickTaskInfo: {},
      taskInfoDrawer: false, //详情展示
      pickerOptions: {
        shortcuts: [
          {
            text: '最近三天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      queryArgs: {
        authQueryParam1: 1,
        collaborator: '',
        attUser: [],
        executeUser: [],
        belongPost: [],
        taskStatus: '',
        prdtCode: [],
        taskName: '',
        taskLevel: '',
        taskTimeFrom: '',
        taskTimeTo: '',
        bizTimeFrom: '',
        bizTimeTo: '',
        residueTime: '',
      },
      queryFormArgs: {},
      attUser: [],
      userList: [],
      postList: [],
      taskStatusList:[],
      taskLevelList: [],
      prdtCode: [],
      collapsed: true,
      // 自动任务并且人工确认 添加行背景色
      addLineBgGridOptions: {
        getRowStyle: params => {
          if (params.data.isCompletedByPerson && params.data.isCompletedByPerson === '1') {
            return { background: '#f8dff1' };
          }
          return null;
        }
      },
    }
  },
  created() {
    this.reSetSearch()
  },
  mounted() {
    this.getAttUser();
    this.getPost();
    this.getPrdt();
    this.taskStatusList = this.$app.dict.getDictItems('TASK_CENTER_STATUS');
    this.taskLevelList = this.$app.dict.getDictItems('AGNES_URGENCY_TYPE');
  },
  methods: {
    async handlePostChange(val) {
      this.attUser = val.length > 0 ? await this.getAttUserByPost(val) : this.userList;
    },
    async getAttUserByPost(arr) {
      // 获取岗位下的用户
      const userRes = await this.$api.bizPostConfigApi.listUserByPostIds({postIds: arr});
      return userRes.data?.map(_ => ({userId: _.userId, userName: _.username}));
    },
    jumpPage(){
      let viewId = 'agnes.task.detail.view';
      this.$agnesUtils.closeTab(viewId);
    },
    showTaskDetail(params) {
      let tempData = params.data;
      tempData.taskCode = params.data.pkId;
      tempData.disType = params.data.disType;
      // 外部任务也不支持临时任务详情查看
      if(params.data.taskType === 'temp' || params.data.disType === 'external'){
           this.$message.warning('暂不支持临时任务详情查看')
      }else{
        this.$nav.showDialog(
          processSceneTask,
          {
            args: {
                    mode: 'view',
                    tempData:JSON.stringify(tempData)
                },
            width: '80%',
            title: params.data.taskName,
            className: 'card-detail'
          }
        )
      }
    },
    /**
     * 任务日期改变
     */
    taskTimeChange(date) {
      if (date) {
        this.queryArgs.taskTimeFrom = date[0];
        this.queryArgs.taskTimeTo = date[1];
      } else {
        this.queryArgs.taskTimeFrom = '';
        this.queryArgs.taskTimeTo = '';
      }
    },
    /**
     * 业务日期改变
     */
    bizTimeChange(date) {
      if (date) {
        this.queryArgs.bizTimeFrom = date[0];
        this.queryArgs.bizTimeTo = date[1];
      } else {
        this.queryArgs.bizTimeFrom = '';
        this.queryArgs.bizTimeTo = '';
      }
    },
    reSetSearch() {
      this.queryArgs = {
        authQueryParam1: 1,
        collaborator: '',
        attUser: [],
        executeUser: [],
        belongPost: [],
        taskStatus: '',
        prdtCode: [],
        taskName: '',
        taskLevel: '',
        taskTimeFrom: '',
        taskTimeTo: '',
        bizTimeFrom: '',
        bizTimeTo: '',
        residueTime: '',
      }
      this.taskTimeRange = this.getDefaultTodayRange()
      this.bizTimeRange = []
      this.queryArgs.taskTimeFrom = this.taskTimeRange[0] || ''
      this.queryArgs.taskTimeTo = this.taskTimeRange[1] || ''
      this.queryArgs.bizTimeFrom = this.bizTimeRange[0] || ''
      this.queryArgs.bizTimeTo = this.bizTimeRange[1] || ''
      this.queryFormArgs = this.$utils.deepClone(this.queryArgs)
      this.queryFormArgs.attUser = '';
      this.queryFormArgs.executeUser = '';
      this.queryFormArgs.belongPost = '';
      this.queryFormArgs.prdtCode = '';
      this.$nextTick(() => {
        this.reloadData()
      });
    },
    getQueryData() {
      this.queryFormArgs = { ...this.queryArgs }
      this.queryFormArgs.belongPost = this.queryFormArgs.belongPost.length > 0
        ? this.queryArgs.belongPost.join(',') : '';
      this.queryFormArgs.executeUser = this.queryFormArgs.executeUser.length > 0
        ? this.queryArgs.executeUser.join(',') : '';
      this.queryFormArgs.attUser = this.queryFormArgs.attUser.length > 0
        ? this.queryArgs.attUser.join(',') : '';
      this.queryFormArgs.prdtCode = this.queryFormArgs.prdtCode.length > 0
        ? this.queryArgs.prdtCode.join(',') : '';
      this.$nextTick(() => {
        this.reloadData()
      })
    },
    reloadData() {
      this.$refs.taskGrid.reloadData(true);
    },
    hideQueryParam() {
      this.collapsed = !this.collapsed
    },
    async getAttUser() {
      try {
        let resp = await this.$api.bizOrgFrameworkApi.getAllUserByUserId({userId: this.$app.session.data.user.userId});

        if (resp.success) {
          this.attUser = resp.data
          this.userList = resp.data;
        } else {
          this.$msg.error(resp.message || '获取责任人列表失败')
        }
      } catch (e) {
        this.$msg.error(e)
      }
    },
    async getPost() {
      try {
        let resp = await this.$api.TaskDetailApi.getPost()
        if (resp.success) {
          this.postList = resp.data
        } else {
          this.$msg.error(resp.message || '获取岗位列表失败')
        }
      } catch (e) {
        this.$msg.error(e)
      }
    },
    getDefaultTodayRange() {
      const todayStart = new Date(); // 获取当前日期
      todayStart.setHours(0, 0, 0, 0); // 设置时间为当天开始
      const todayEnd = new Date(); // 再次获取当前日期
      todayEnd.setHours(23, 59, 59, 999); // 设置时间为当天结束
      return [this.formatDate(todayStart), this.formatDate(todayEnd)];
    },
    formatDate(date) {
      // 格式化日期为 yyyy-MM-dd
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    async getPrdt() {
      try {
        let resp = await this.$api.TaskDetailApi.getPrdt()
        if (resp.success) {
          this.prdtCode = resp.data
        } else {
          this.$msg.error(resp.message || '获取产品代码失败')
        }
      } catch (e) {
        this.$msg.error(e)
      }
    },
  },
}
</script>
<style lang="less" scoped>
.el-date-editor {
  border-color: #a8aed3 !important;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 8px; /* 按钮之间的间距 */
  position: absolute;
  &.button-fold{
    right: 0;
    top: -50px;
  }
  &.button-un-fold{
    right: 10px;
    top: -7px;
  }
}

.option-btn {
  margin-left: 8px;
}

.task-detail-view{
  height: 100%;

  .task-query-fold{
    height: 100px;
  }
  .task-query-unFold{
    height: 160px;
  }
}

.return-button {
    display: flex;
    justify-content: flex-end;
    top: 10px;
    right: 10px;
    height: 40px;
}

.add-btn{
    color: #0f5eff;
    font-size: 12px;
    border: 1px solid #0f5eff;
    border-radius: 4px;
    background: transparent;
    padding: 6px 10px;
    margin-bottom: 10px;
}
/* 表格样式 竖向滚动条*/
.task-detail-view /deep/ .ag-body-viewport.ag-layout-normal::-webkit-scrollbar{
  display: block !important;
}
</style>

<template>
    <div>
        <el-form ref="form" :model="form" :rules="rules" @submit.native.prevent label-width="90px">
            <div class="line">
                <el-form-item label="任务名称:" prop="name">
                    <gf-input v-model="form.name" type="text"></gf-input>
                </el-form-item>
            </div>
            <div class="line">
                <el-form-item label="配置方式:" prop="disType">
                    <el-radio v-model="form.disType" v-removeAria label="0">场景任务</el-radio>
                    <el-radio v-model="form.disType" v-removeAria label="1">流程任务</el-radio>
                    <el-radio v-model="form.disType" v-removeAria label="2">单节点任务</el-radio>
                </el-form-item>
            </div>
        </el-form>
        <dialog-footer :on-cancel="onCancel" :on-save="onSave" okButtonTitle="下一步"></dialog-footer>
    </div>
</template>

<script>
export default {
    name: "add-obj-dialog",
    props: {
        actionOk: Function,
        oldData:  {
            type: Object
        }
    },
    data() {
        return {
            form: {
                name: '',
                disType: '0'
            },
            rules: {
                name: { required: true, message: '请输入任务名称', trigger: 'change' },
                disType: { required: true, message: '请选择配置方式', trigger: 'change' },
            }
        }
    },
    mounted() {
        if(this.oldData &&this.oldData.reTaskDef && this.oldData.reTaskDef.taskName){
            this.oldData.name = this.oldData.reTaskDef.taskName+'(copy)'
            this.form = {...this.oldData};
            this.form.disType = this.form.reTaskDef.disType
        }
    },
    methods: {
        onCancel() {
            this.$dialog.close(this, 'cancel');
        },
        async onSave(event) {
            event.preventDefault();
            const ok = await this.$refs.form.validate();
            if(ok){
                this.onCancel()
                // this.pageJump('agnes.work.flow',this.form, this.actionOk, '流程配置', 'add')
                await this.actionOk(this.form,'regularAdd')
            }
        },
        pageJump(url,row, actionOk, title, mode){
            let viewId = url;
            this.$agnesUtils.closeTab(viewId);
            this.$nextTick(() => {
                let pageView = this.$app.views.getView(viewId);
                let tabView = Object.assign(
                    {
                        args: {
                            row: row,
                            mode: mode,
                            title: title,
                            actionOk: actionOk,
                            addType: this.addType
                        },
                        id: viewId
                    }, pageView);

                this.$nav.showView(tabView);
            })
        }
    }
}
</script>

<style scoped>
.dialog-footer-bar >>> .el-button {
    width: 80px;
}

</style>

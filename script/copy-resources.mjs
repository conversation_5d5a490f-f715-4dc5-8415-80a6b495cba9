/*
 * @Author: <EMAIL>
 * @Date: 2024-12-31 11:33:32
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2025-01-07 11:05:27
 * @Description: 
 */
/**
 * <AUTHOR>
 * @since 2023-07-04
 */
const fsResp = import("node:fs");

const luckysheetDistDir = "public/luckysheet-dist";
const pdfjsDistDir = "public/pdfjs-dist";
fsResp.then((fs) => {
  if (fs.existsSync(pdfjsDistDir)) {
    fs.rmdirSync(pdfjsDistDir, { recursive: true });
  }
  fs.cpSync(`node_modules/@hexinfo/x-doc-ui/${pdfjsDistDir}`, pdfjsDistDir, { recursive: true });

  if (fs.existsSync(luckysheetDistDir)) {
    fs.rmdirSync(luckysheetDistDir, { recursive: true });
  }
  fs.cpSync(`node_modules/@hexinfo/x-doc-ui/${luckysheetDistDir}`, luckysheetDistDir, { recursive: true });
});
